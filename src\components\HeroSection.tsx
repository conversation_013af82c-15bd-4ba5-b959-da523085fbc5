import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Loader } from 'lucide-react';

interface Artist {
  id: string;
  name: string;
  image: string;
  genres: string[];
}

const artists: Artist[] = [
  {
    id: '1',
    name: 'DJ Nexus',
    image: 'https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=300&h=400&fit=crop&crop=center',
    genres: ['Electronic', 'House']
  },
  {
    id: '2',
    name: 'Luna Waves',
    image: 'https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=300&h=400&fit=crop&crop=center',
    genres: ['Techno', 'Progressive']
  },
  {
    id: '3',
    name: 'Echo Storm',
    image: 'https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=300&h=400&fit=crop&crop=center',
    genres: ['House', 'Electronic']
  },
  {
    id: '4',
    name: 'Rhythm Pulse',
    image: 'https://images.unsplash.com/photo-1501286353178-1ec881214838?w=300&h=400&fit=crop&crop=center',
    genres: ['Progressive', 'Techno']
  },
  {
    id: '5',
    name: 'Sonic Drift',
    image: 'https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=300&h=400&fit=crop&crop=center',
    genres: ['Electronic', 'House']
  },
  {
    id: '6',
    name: 'Neon Circuit',
    image: 'https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=300&h=400&fit=crop&crop=center',
    genres: ['Techno', 'Electronic']
  }
];

const genres = ['All', 'Electronic', 'House', 'Techno', 'Progressive'];

const HeroSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [selectedGenre, setSelectedGenre] = useState('All');
  const [displayedArtists, setDisplayedArtists] = useState(artists);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const filteredArtists = selectedGenre === 'All' 
    ? artists 
    : artists.filter(artist => artist.genres.includes(selectedGenre));

  const handleGenreClick = (genre: string) => {
    if (genre !== selectedGenre) {
      setIsTransitioning(true);
      
      // Phase 1: Fade out current artists
      setTimeout(() => {
        setSelectedGenre(genre);
        const newFilteredArtists = genre === 'All' 
          ? artists 
          : artists.filter(artist => artist.genres.includes(genre));
        setDisplayedArtists(newFilteredArtists);
      }, 250);
      
      // Phase 2: Fade in new artists
      setTimeout(() => {
        setIsTransitioning(false);
      }, 300);
    }
  };

  // Update displayed artists when not transitioning
  useEffect(() => {
    if (!isTransitioning) {
      setDisplayedArtists(filteredArtists);
    }
  }, [filteredArtists, isTransitioning]);

  return (
    <section className="relative w-full py-12 md:py-20 px-6 md:px-12 flex flex-col items-center justify-center overflow-hidden">
      <div className={`relative z-10 max-w-4xl text-center space-y-6 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
        <div className="flex justify-center">
          <Link to="/contact">
            <span className="inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded-full bg-muted text-primary hover:bg-muted/80 transition-colors cursor-pointer">
              <span className="flex h-2 w-2 rounded-full bg-primary"></span>
              Are you an artist? Let's talk!
            </span>
          </Link>
        </div>
        
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-semibold tracking-tighter text-balance text-foreground">
          Exceptional <span className="text-primary">artist</span> management.
        </h1>
        
        <div className="flex justify-center pt-2">
          <Link to="/agency">
            <button className="group inline-flex items-center gap-2 text-lg md:text-xl text-foreground hover:text-primary transition-all duration-200 cursor-pointer">
              <span>Discover what we do</span>
              <svg 
                className="w-5 h-5 transition-transform duration-200 group-hover:translate-x-1" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </Link>
        </div>
        
        {/* Genre Filter System */}
        <div className="pt-10 flex flex-wrap justify-center gap-2">
          {genres.map((genre) => (
            <span
              key={genre}
              onClick={() => handleGenreClick(genre)}
              className={`px-3 py-1 text-xs font-medium rounded-full cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                selectedGenre === genre
                  ? 'bg-primary text-primary-foreground hover:bg-primary/80 scale-105'
                  : 'border border-border text-foreground hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {genre}
            </span>
          ))}
        </div>
      </div>
      
      {/* Artist Grid */}
      <div className={`w-full max-w-5xl mx-auto mt-12 z-10 transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}>
        <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-center transition-all duration-300`}>
          {displayedArtists.map((artist, index) => (
            <Link 
              key={artist.id}
              to={`/artist/${artist.id}`}
              className={`group relative overflow-hidden rounded-xl bg-card border border-border hover:border-primary/30 transition-all duration-300 cursor-pointer hover:scale-102 block ${
                !isTransitioning ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-4 scale-95'
              }`}
              style={{
                transitionDelay: isTransitioning ? '0ms' : `${index * 50}ms`
              }}
            >
              <div className="aspect-[3/4] overflow-hidden">
                <img
                  src={artist.image}
                  alt={artist.name}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <h3 className="font-semibold text-lg mb-2">{artist.name}</h3>
                  <div className="flex gap-1 flex-wrap">
                    {artist.genres.map((genre) => (
                      <span key={genre} className="px-2 py-1 text-xs font-medium rounded-full border border-white/50 text-white hover:bg-white/20 transition-colors">
                        {genre}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
