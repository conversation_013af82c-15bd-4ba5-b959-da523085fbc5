import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

const AnnouncementBanner = () => {
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    const bannerDismissed = sessionStorage.getItem('announcement-banner-dismissed');
    if (!bannerDismissed) {
      setShowBanner(true);
    }
  }, []);

  const handleDismiss = () => {
    setShowBanner(false);
    sessionStorage.setItem('announcement-banner-dismissed', 'true');
  };

  if (!showBanner) return null;

  return (
    <div className="fixed top-4 right-4 z-[100] max-w-xs">
      <Alert className="bg-blue-50/95 dark:bg-blue-950/95 border-blue-200 dark:border-blue-800 backdrop-blur-sm shadow-lg py-2 px-3">
        <AlertDescription className="flex items-center justify-between text-sm">
          <span className="font-medium text-blue-800 dark:text-blue-200 pr-2">
            Stagecloud. The new to book DJ's. Coming soon 🚀
          </span>
          <button
            onClick={handleDismiss}
            className="text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100 transition-colors flex-shrink-0"
            aria-label="Dismiss notification"
          >
            <X className="h-3 w-3" />
          </button>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default AnnouncementBanner;