import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import Header from '@/components/Header';
import CTAStrip from '@/components/CTAStrip';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { 
  Calendar, 
  Download, 
  MapPin, 
  Music, 
  Play, 
  Star, 
  Users, 
  ChevronLeft, 
  ChevronRight,
  Send 
} from 'lucide-react';

const Artist = () => {
  const { id } = useParams();
  const { toast } = useToast();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [bookingForm, setBookingForm] = useState({
    name: '',
    email: '',
    date: '',
    venue: '',
    budget: '',
    message: ''
  });

  // Mock artist data - will be dynamic later
  const artistImages = [
    "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop",
    "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop",
    "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=800&h=600&fit=crop"
  ];

  const artistData = {
    name: "DJ Alex Martinez",
    genre: "Progressive House",
    location: "Amsterdam, Netherlands",
    rating: 4.9,
    totalShows: 250,
    experience: "8 years",
    bio: "Alex Martinez is a world-renowned DJ specializing in progressive house music. With over 8 years of experience performing at top venues and festivals worldwide, Alex brings an unmatched energy to every performance.",
    achievements: [
      "Headlined at Tomorrowland 2023",
      "Resident DJ at Club Amnesia",
      "Over 50M streams on Spotify",
      "Winner of DJ Awards 2022"
    ],
    references: [
      {
        name: "Ultra Music Festival",
        comment: "Alex delivered an incredible performance that had the crowd dancing all night. Professional and reliable.",
        rating: 5
      },
      {
        name: "Ministry of Sound",
        comment: "One of the best DJs we've worked with. Always brings amazing energy and reads the crowd perfectly.",
        rating: 5
      }
    ]
  };

  const handleBookingSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Booking Request Sent!",
      description: "We'll get back to you within 24 hours with availability and pricing."
    });
    setBookingForm({
      name: '',
      email: '',
      date: '',
      venue: '',
      budget: '',
      message: ''
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setBookingForm(prev => ({ ...prev, [name]: value }));
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % artistImages.length);
  };

  const previousImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + artistImages.length) % artistImages.length);
  };

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground relative overflow-hidden">
      {/* Cosmic particle effect (background dots) */}
      <div className="absolute inset-0 cosmic-grid opacity-30"></div>
      
      {/* Gradient glow effect */}
      <div className="absolute top-[450px] left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full">
        <div className="w-full h-full opacity-10 bg-primary blur-[120px]"></div>
      </div>
      
      <div className="relative z-10 flex flex-col min-h-screen">
        <Header />
        <main className="flex-1">
          {/* Hero Section with Image Carousel and Booking Form */}
          <section className="py-12 px-6 md:px-12">
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Image Carousel - 2/3 */}
                <div className="lg:col-span-2">
                  <div className="relative rounded-xl overflow-hidden bg-card">
                    <div className="aspect-[4/3] relative">
                      <img
                        src={artistImages[currentImageIndex]}
                        alt={`${artistData.name} - Image ${currentImageIndex + 1}`}
                        className="w-full h-full object-cover"
                      />
                      {/* Navigation Arrows */}
                      <button
                        onClick={previousImage}
                        className="absolute left-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
                      >
                        <ChevronLeft className="h-5 w-5" />
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
                      >
                        <ChevronRight className="h-5 w-5" />
                      </button>
                      {/* Image Indicators */}
                      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
                        {artistImages.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentImageIndex(index)}
                            className={`w-2 h-2 rounded-full transition-colors ${
                              index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Booking Form - 1/3 */}
                <div className="lg:col-span-1">
                  <Card className="sticky top-8">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-2xl">{artistData.name}</CardTitle>
                          <CardDescription className="text-lg">{artistData.genre}</CardDescription>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="font-semibold">{artistData.rating}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {artistData.location}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {artistData.totalShows} shows
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <form onSubmit={handleBookingSubmit} className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">Full Name *</Label>
                          <Input
                            id="name"
                            name="name"
                            value={bookingForm.name}
                            onChange={handleInputChange}
                            placeholder="Your full name"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email">Email *</Label>
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            value={bookingForm.email}
                            onChange={handleInputChange}
                            placeholder="<EMAIL>"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="date">Event Date *</Label>
                          <Input
                            id="date"
                            name="date"
                            type="date"
                            value={bookingForm.date}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="venue">Venue/Location *</Label>
                          <Input
                            id="venue"
                            name="venue"
                            value={bookingForm.venue}
                            onChange={handleInputChange}
                            placeholder="Venue name and city"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="budget">Budget Range</Label>
                          <Input
                            id="budget"
                            name="budget"
                            value={bookingForm.budget}
                            onChange={handleInputChange}
                            placeholder="€5,000 - €10,000"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="message">Additional Details</Label>
                          <Textarea
                            id="message"
                            name="message"
                            value={bookingForm.message}
                            onChange={handleInputChange}
                            placeholder="Tell us about your event..."
                            rows={3}
                          />
                        </div>
                        <Button type="submit" className="w-full">
                          <Send className="h-4 w-4 mr-2" />
                          Request Booking
                        </Button>
                      </form>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </section>

          {/* Artist Information Section */}
          <section className="py-12 px-6 md:px-12">
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Main Content - 2/3 */}
                <div className="lg:col-span-2 space-y-8">
                  {/* About Section */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Music className="h-5 w-5" />
                        About {artistData.name}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground leading-relaxed mb-6">
                        {artistData.bio}
                      </p>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-primary">{artistData.experience}</div>
                          <div className="text-sm text-muted-foreground">Experience</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-primary">{artistData.totalShows}+</div>
                          <div className="text-sm text-muted-foreground">Shows Performed</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-primary">{artistData.rating}</div>
                          <div className="text-sm text-muted-foreground">Average Rating</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Achievements */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Career Highlights</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {artistData.achievements.map((achievement, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span className="text-sm">{achievement}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* References */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Client References</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {artistData.references.map((reference, index) => (
                        <div key={index} className="border-l-4 border-primary pl-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-semibold">{reference.name}</h4>
                            <div className="flex items-center gap-1">
                              {[...Array(reference.rating)].map((_, i) => (
                                <Star key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              ))}
                            </div>
                          </div>
                          <p className="text-muted-foreground text-sm italic">"{reference.comment}"</p>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                </div>

                {/* Sidebar - 1/3 */}
                <div className="lg:col-span-1 space-y-6">
                  {/* Press Kit Download */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Press Kit</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground text-sm mb-4">
                        Download high-resolution photos, biography, and technical rider.
                      </p>
                      <Button variant="outline" className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        Download Press Kit
                      </Button>
                    </CardContent>
                  </Card>

                  {/* Quick Stats */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Quick Stats</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Genre:</span>
                        <Badge variant="secondary">{artistData.genre}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Based in:</span>
                        <span className="text-sm">{artistData.location}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Experience:</span>
                        <span className="text-sm">{artistData.experience}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Shows:</span>
                        <span className="text-sm">{artistData.totalShows}+</span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Audio Preview */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Latest Mix</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                        <div>
                          <div className="font-medium text-sm">Summer Vibes 2024</div>
                          <div className="text-xs text-muted-foreground">60 min mix</div>
                        </div>
                        <Button size="sm" variant="ghost">
                          <Play className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </section>
        </main>
        <CTAStrip />
        <Footer />
      </div>
    </div>
  );
};

export default Artist;