
import React from 'react';

const Logo = () => {
  return (
    <div className="flex items-center">
      {/* Light mode icon (black) - shown when NOT in dark-mode */}
      <img
        src="/logo/Icon_black_bgtrans.png"
        alt="Crash Events"
        className="h-6 w-auto logo-light"
      />
      {/* Dark mode icon (white) - shown when in dark-mode */}
      <img
        src="/logo/Icon_white_bgtrans.png"
        alt="Crash Events"
        className="h-6 w-auto logo-dark"
      />
    </div>
  );
};

export default Logo;
