
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 48% 13%; /* Deep navy #10182D */
    --foreground: 0 0% 96%; /* Light text #F5F5F5 */

    --card: 220 48% 20%; /* Medium navy #1A284B */
    --card-foreground: 0 0% 96%;

    --popover: 220 48% 20%;
    --popover-foreground: 0 0% 96%;

    --primary: 327 60% 53%; /* Brand pink #CD408E */
    --primary-foreground: 0 0% 96%;

    --secondary: 220 48% 20%; /* Medium navy */
    --secondary-foreground: 0 0% 96%;

    --muted: 220 30% 25%; /* Muted navy */
    --muted-foreground: 0 0% 72%; /* Medium grey #B8B8B8 */

    --accent: 327 60% 53%; /* Brand pink accent */
    --accent-foreground: 0 0% 96%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 96%;

    --border: 220 30% 25%; /* Muted navy border */
    --input: 220 48% 20%;
    --ring: 327 60% 53%; /* Brand pink ring */

    --radius: 0.5rem;
  }

  .light-mode {
    --background: 0 0% 96%; /* Light background #F5F5F5 */
    --foreground: 220 48% 13%; /* Deep navy text */

    --card: 0 0% 98%; /* Very light card */
    --card-foreground: 220 48% 13%;

    --popover: 0 0% 98%;
    --popover-foreground: 220 48% 13%;

    --primary: 327 60% 53%; /* Brand pink primary */
    --primary-foreground: 0 0% 96%;

    --secondary: 0 0% 94%; /* Light secondary */
    --secondary-foreground: 220 48% 13%;

    --muted: 0 0% 90%; /* Light muted */
    --muted-foreground: 0 0% 45%; /* Darker muted text */

    --accent: 327 60% 53%; /* Brand pink accent */
    --accent-foreground: 0 0% 96%;

    --border: 0 0% 88%; /* Light border */
    --input: 0 0% 92%;
    --ring: 327 60% 53%; /* Brand pink ring */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background font-poppins font-normal text-foreground antialiased;
    letter-spacing: -0.01em;
  }
  
  ::selection {
    @apply bg-primary text-primary-foreground;
  }

  .cosmic-gradient {
    background: linear-gradient(135deg, rgba(26, 40, 75, 0.3) 0%, rgba(16, 24, 45, 0.5) 100%);
  }

  .light-mode .cosmic-gradient {
    background: linear-gradient(135deg, rgba(245, 245, 245, 0.8) 0%, rgba(184, 184, 184, 0.4) 100%);
  }

  .cosmic-glow {
    position: relative;
  }

  .cosmic-glow::before {
    content: "";
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle at center, rgba(205, 64, 142, 0.1) 0%, transparent 70%);
    z-index: -1;
    border-radius: inherit;
  }

  .light-mode .cosmic-glow::before {
    background: radial-gradient(circle at center, rgba(205, 64, 142, 0.05) 0%, transparent 70%);
  }

  .cosmic-grid {
    background-image: 
      linear-gradient(rgba(205, 64, 142, 0.08) 1px, transparent 1px),
      linear-gradient(90deg, rgba(205, 64, 142, 0.08) 1px, transparent 1px);
    background-size: 30px 30px;
  }

  .light-mode .cosmic-grid {
    background-image: 
      linear-gradient(rgba(16, 24, 45, 0.08) 1px, transparent 1px),
      linear-gradient(90deg, rgba(16, 24, 45, 0.08) 1px, transparent 1px);
  }

  .text-balance {
    text-wrap: balance;
  }
  
  .cosmic-glass {
    @apply backdrop-blur-sm bg-card border border-white/10 shadow-[0_0_15px_rgba(255,255,255,0.1)];
  }

  .light-mode .cosmic-glass {
    @apply backdrop-blur-sm bg-card border border-black/10 shadow-[0_0_15px_rgba(0,0,0,0.05)];
  }
  
  .cosmic-card {
    @apply bg-card border border-border backdrop-blur-sm;
  }
  
  .nav-pill {
    @apply backdrop-blur-md bg-card border border-border rounded-full;
  }
  
  .nav-pill-item {
    @apply px-4 py-2 text-sm rounded-full transition-colors;
  }
  
  .nav-pill-item:hover {
    @apply bg-accent text-accent-foreground;
  }
  
  .nav-pill-item.active {
    @apply bg-accent text-accent-foreground;
  }
  
  .icon-glow {
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
    transition: filter 0.3s ease;
  }

  .light-mode .icon-glow {
    filter: drop-shadow(0 0 6px rgba(64, 64, 64, 0.2));
  }
  
  .collapsible-trigger:hover .icon-glow {
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
  }

  .light-mode .collapsible-trigger:hover .icon-glow {
    filter: drop-shadow(0 0 10px rgba(64, 64, 64, 0.3));
  }

  .task-card {
    @apply transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-lg hover:border-primary/30;
  }
  
  .task-card.dragging {
    @apply scale-105 shadow-lg border-primary opacity-70 rotate-1 z-10;
  }
  
  .task-card.dragged-over {
    @apply border-primary border-dashed;
  }
  
  .task-card-enter {
    @apply opacity-0 -translate-y-4;
  }
  
  .task-card-enter-active {
    @apply opacity-100 translate-y-0 transition-all duration-300 ease-out;
  }
  
  .task-card-exit {
    @apply opacity-100;
  }
  
  .task-card-exit-active {
    @apply opacity-0 translate-y-4 transition-all duration-300 ease-in;
  }
  
  .column-highlight {
    @apply bg-accent/20 transition-all duration-300;
  }
}
