import React, { useState, useEffect } from 'react';
import Header from '@/components/Header';
import CTAStrip from '@/components/CTAStrip';
import Footer from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';

const About = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      name: "<PERSON>",
      role: "Head of Booking",
      image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      name: "<PERSON> <PERSON><PERSON>",
      role: "Artist Development Director",
      image: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      name: "James Kim",
      role: "Digital Strategy Lead",
      image: "https://images.unsplash.com/photo-1501286353178-1ec881214838?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      name: "Luna Petrov",
      role: "Creative Director",
      image: "https://images.unsplash.com/photo-1518005020951-eccb494ad742?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      name: "David Thompson",
      role: "Tour Manager",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground relative overflow-hidden">
      {/* Cosmic particle effect (background dots) */}
      <div className="absolute inset-0 cosmic-grid opacity-30"></div>
      
      {/* Gradient glow effect */}
      <div className="absolute top-[450px] left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full">
        <div className="w-full h-full opacity-10 bg-primary blur-[120px]"></div>
      </div>
      
      <div className="relative z-10 flex flex-col min-h-screen">
        <Header />
        <main className="flex-1">
          {/* Hero Section */}
          <section className="relative w-full py-20 md:py-32 px-6 md:px-12 flex flex-col items-center justify-center">
            <div className={`relative z-10 max-w-5xl text-center space-y-8 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
              <div className="flex justify-center">
                <span className="inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded-full bg-muted text-primary">
                  <span className="flex h-2 w-2 rounded-full bg-primary"></span>
                  Our Story
                </span>
              </div>
              
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-semibold tracking-tighter text-balance text-foreground">
                Meet the <span className="text-primary">Team</span>
              </h1>
              
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Passionate music industry professionals dedicated to elevating electronic music artists worldwide.
              </p>
            </div>
          </section>

        {/* Mission Section */}
        <section className="py-16 px-6 md:px-12">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <h2 className="text-3xl md:text-4xl font-semibold">Our Mission</h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              Founded in 2014, our agency emerged from a shared vision to bridge the gap between exceptional electronic music talent and the global stage. We believe that great music transcends boundaries, and our mission is to provide artists with the tools, connections, and strategic guidance they need to reach audiences worldwide.
            </p>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-16 px-6 md:px-12">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {teamMembers.map((member, index) => (
                <Card key={index} className="border-border bg-card hover:border-primary/20 transition-all duration-300 overflow-hidden">
                  <div className="aspect-square overflow-hidden">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardContent className="p-8 space-y-4 text-left">
                    <h3 className="text-2xl font-semibold">{member.name}</h3>
                    <p className="text-primary font-medium">{member.role}</p>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p>{member.email}</p>
                      <p>{member.phone}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      </main>
      <CTAStrip />
      <Footer />
      </div>
    </div>
  );
};

export default About;